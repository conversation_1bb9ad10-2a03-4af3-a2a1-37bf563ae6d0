# AI Helper Setup Guide for DERS

## Overview
The DERS application now uses a unified AI helper that supports multiple AI providers (OpenAI, Gemini, Claude) for document processing and text extraction.

## Environment Configuration

Add these lines to your `.env` file:

```env
# AI Configuration for DERS
GEMINI_API_KEY=AIzaSyD-your-gemini-api-key-here
OPENAI_API_KEY=sk-proj-your-openai-key-here
CLAUDE_API_KEY=sk-ant-your-claude-key-here

# Default AI Provider (optional)
DEFAULT_AI_PROVIDER=gemini
```

## How to Get API Keys

### Google Gemini API Key
1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account
3. Click "Get API Key"
4. Create a new API key
5. Copy the key (starts with `AIzaSy`)

### OpenAI API Key (Optional)
1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in
3. Go to API Keys section
4. Create new secret key
5. Copy the key (starts with `sk-proj-` or `sk-`)

### Anthropic Claude API Key (Optional)
1. Go to [Anthropic Console](https://console.anthropic.com/)
2. Sign up or log in
3. Go to API Keys section
4. Create new key
5. Copy the key (starts with `sk-ant-`)

## Current Implementation

The applicant file upload page (`app/Views/applicant/applicant_files_create.php`) now uses:

- **AI Helper Functions**: Located in `app/Helpers/ai_helper.php`
- **Controller Methods**: `ApplicantController::aiExtractText()` and `ApplicantController::aiGenerateTitle()`
- **Routes**: 
  - `POST /applicant/profile/ai/extract-text`
  - `POST /applicant/profile/ai/generate-title`

## Features

1. **PDF Text Extraction**: Uses Gemini Vision AI to extract text from PDF pages
2. **Title Generation**: AI-powered title and description generation
3. **Multi-page Processing**: Handles documents with multiple pages
4. **Error Handling**: Comprehensive error handling and validation
5. **Rate Limiting**: Built-in delays to prevent API rate limiting

## Usage in Code

```php
// Load the AI helper
helper('ai');

// Extract text using Gemini Vision
$response = ai_gemini_vision($base64_image, $prompt);

// Generate chat response
$response = ai_gemini_chat($prompt);

// Validate response
$validation = ai_validate_response($response, 'gemini');
if ($validation['valid']) {
    echo $validation['content'];
}
```

## JavaScript Integration

The frontend JavaScript now sends requests to the AI endpoints instead of making direct API calls:

```javascript
// Extract text from images
const response = await fetch('/applicant/profile/ai/extract-text', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
        images: imageData,
        prompt: extractionPrompt
    })
});

// Generate title and description
const response = await fetch('/applicant/profile/ai/generate-title', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
        prompt: titlePrompt
    })
});
```

## Security Features

1. **CSRF Protection**: All AI endpoints require CSRF tokens
2. **Authentication**: Only authenticated applicants can access AI features
3. **Input Validation**: Comprehensive validation of all inputs
4. **Error Logging**: All errors are logged for monitoring

## Testing

To test the AI integration:

1. Ensure your Gemini API key is set in `.env`
2. Navigate to `/applicant/profile/files/create`
3. Upload a PDF file
4. The system will automatically process the file using AI
5. Check the browser console and server logs for any errors

## Troubleshooting

### Common Issues

1. **"Authentication required" error**: Make sure you're logged in as an applicant
2. **"Invalid request method" error**: Ensure requests are made via AJAX/fetch
3. **"AI processing failed" error**: Check your API key and internet connection
4. **CSRF token errors**: Ensure the CSRF meta tag is present in the page head

### Debug Steps

1. Check server logs: `tail -f writable/logs/log-*.php`
2. Check browser console for JavaScript errors
3. Verify API key is correctly set in `.env`
4. Test API key with a simple curl request

## Performance Notes

- Processing time depends on document size and complexity
- Large documents (20+ pages) may take 5+ minutes
- The system includes progress indicators and timing information
- Rate limiting prevents API quota exhaustion

## Future Enhancements

- Support for additional AI providers
- Batch processing optimization
- Caching of AI responses
- Advanced document analysis features
