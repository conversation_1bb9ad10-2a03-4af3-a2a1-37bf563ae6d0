<?php

/**
 * AI Helper for DERS (Dakoii Echad Recruitment & Selection System)
 * Supports multiple AI LLMs: OpenAI, Gemini, Claude, etc.
 * 
 * Usage:
 * - ai_openai_chat($messages, $options)
 * - ai_gemini_chat($prompt, $options)
 * - ai_claude_chat($messages, $options)
 * - ai_openai_vision($image_data, $prompt, $options)
 * - ai_gemini_vision($image_data, $prompt, $options)
 */

if (!function_exists('ai_get_config')) {
    /**
     * Get AI configuration settings
     */
    function ai_get_config($provider = null) {
        $config = [
            'openai' => [
                'api_key' => env('OPENAI_API_KEY', 'sk-proj-your-openai-key-here'),
                'base_url' => 'https://api.openai.com/v1',
                'models' => [
                    'chat' => 'gpt-4o',
                    'vision' => 'gpt-4o',
                    'embedding' => 'text-embedding-3-small'
                ],
                'max_tokens' => 4000,
                'temperature' => 0.5
            ],
            'gemini' => [
                'api_key' => env('GEMINI_API_KEY', 'AIzaSyDyour-gemini-key-here'),
                'base_url' => 'https://generativelanguage.googleapis.com/v1beta',
                'models' => [
                    'chat' => 'gemini-2.0-flash-exp',
                    'vision' => 'gemini-2.0-flash-exp',
                    'lite' => 'gemini-2.5-flash-lite-preview-06-17'
                ],
                'max_tokens' => 60192,
                'temperature' => 0.5
            ],
            'claude' => [
                'api_key' => env('CLAUDE_API_KEY', 'sk-ant-your-claude-key-here'),
                'base_url' => 'https://api.anthropic.com/v1',
                'models' => [
                    'chat' => 'claude-3-5-sonnet-20241022',
                    'vision' => 'claude-3-5-sonnet-20241022'
                ],
                'max_tokens' => 4000,
                'temperature' => 0.7
            ]
        ];

        return $provider ? ($config[$provider] ?? null) : $config;
    }
}

if (!function_exists('ai_openai_chat')) {
    /**
     * OpenAI Chat Completion
     * 
     * @param array $messages Array of messages [['role' => 'user', 'content' => 'text']]
     * @param array $options Additional options (model, temperature, max_tokens, etc.)
     * @return array Response with success status and data
     */
    function ai_openai_chat($messages, $options = []) {
        $config = ai_get_config('openai');
        
        $data = [
            'model' => $options['model'] ?? $config['models']['chat'],
            'messages' => $messages,
            'max_tokens' => $options['max_tokens'] ?? $config['max_tokens'],
            'temperature' => $options['temperature'] ?? $config['temperature']
        ];

        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $config['api_key']
        ];

        return ai_make_request($config['base_url'] . '/chat/completions', $data, $headers);
    }
}

if (!function_exists('ai_openai_vision')) {
    /**
     * OpenAI Vision (Image Analysis)
     * 
     * @param string $image_data Base64 encoded image or image URL
     * @param string $prompt Text prompt for image analysis
     * @param array $options Additional options
     * @return array Response with success status and data
     */
    function ai_openai_vision($image_data, $prompt, $options = []) {
        $config = ai_get_config('openai');
        
        $image_url = strpos($image_data, 'http') === 0 ? 
            $image_data : 
            'data:image/jpeg;base64,' . $image_data;

        $messages = [
            [
                'role' => 'user',
                'content' => [
                    ['type' => 'text', 'text' => $prompt],
                    ['type' => 'image_url', 'image_url' => ['url' => $image_url]]
                ]
            ]
        ];

        return ai_openai_chat($messages, $options);
    }
}

if (!function_exists('ai_gemini_chat')) {
    /**
     * Google Gemini Chat
     * 
     * @param string $prompt Text prompt
     * @param array $options Additional options (model, temperature, etc.)
     * @return array Response with success status and data
     */
    function ai_gemini_chat($prompt, $options = []) {
        $config = ai_get_config('gemini');
        $model = $options['model'] ?? $config['models']['chat'];
        
        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => $options['temperature'] ?? $config['temperature'],
                'maxOutputTokens' => $options['max_tokens'] ?? $config['max_tokens']
            ]
        ];

        $url = $config['base_url'] . '/models/' . $model . ':generateContent?key=' . $config['api_key'];
        
        $headers = [
            'Content-Type: application/json'
        ];

        return ai_make_request($url, $data, $headers);
    }
}

if (!function_exists('ai_gemini_vision')) {
    /**
     * Google Gemini Vision (Image Analysis)
     * 
     * @param string $image_data Base64 encoded image
     * @param string $prompt Text prompt for image analysis
     * @param array $options Additional options
     * @return array Response with success status and data
     */
    function ai_gemini_vision($image_data, $prompt, $options = []) {
        $config = ai_get_config('gemini');
        $model = $options['model'] ?? $config['models']['vision'];
        
        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt],
                        [
                            'inline_data' => [
                                'mime_type' => $options['mime_type'] ?? 'image/jpeg',
                                'data' => $image_data
                            ]
                        ]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => $options['temperature'] ?? $config['temperature'],
                'maxOutputTokens' => $options['max_tokens'] ?? $config['max_tokens']
            ]
        ];

        $url = $config['base_url'] . '/models/' . $model . ':generateContent?key=' . $config['api_key'];
        
        $headers = [
            'Content-Type: application/json'
        ];

        return ai_make_request($url, $data, $headers);
    }
}

if (!function_exists('ai_claude_chat')) {
    /**
     * Anthropic Claude Chat
     * 
     * @param array $messages Array of messages
     * @param array $options Additional options
     * @return array Response with success status and data
     */
    function ai_claude_chat($messages, $options = []) {
        $config = ai_get_config('claude');
        
        $data = [
            'model' => $options['model'] ?? $config['models']['chat'],
            'max_tokens' => $options['max_tokens'] ?? $config['max_tokens'],
            'temperature' => $options['temperature'] ?? $config['temperature'],
            'messages' => $messages
        ];

        $headers = [
            'Content-Type: application/json',
            'x-api-key: ' . $config['api_key'],
            'anthropic-version: 2023-06-01'
        ];

        return ai_make_request($config['base_url'] . '/messages', $data, $headers);
    }
}

if (!function_exists('ai_make_request')) {
    /**
     * Make HTTP request to AI API
     * 
     * @param string $url API endpoint URL
     * @param array $data Request data
     * @param array $headers Request headers
     * @return array Response with success status and data
     */
    function ai_make_request($url, $data, $headers) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 240, // 4 minutes timeout
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return [
                'success' => false,
                'error' => 'CURL Error: ' . $error,
                'http_code' => 0
            ];
        }

        $decoded_response = json_decode($response, true);

        if ($http_code >= 200 && $http_code < 300) {
            return [
                'success' => true,
                'data' => $decoded_response,
                'http_code' => $http_code
            ];
        } else {
            return [
                'success' => false,
                'error' => $decoded_response['error']['message'] ?? 'Unknown API error',
                'http_code' => $http_code,
                'raw_response' => $response
            ];
        }
    }
}

if (!function_exists('ai_extract_text_from_pdf')) {
    /**
     * Extract text from PDF using AI (works with any AI provider)
     *
     * @param string $pdf_path Path to PDF file
     * @param string $provider AI provider ('openai', 'gemini', 'claude')
     * @param array $options Additional options
     * @return array Response with extracted text
     */
    function ai_extract_text_from_pdf($pdf_path, $provider = 'gemini', $options = []) {
        // Convert PDF to images first
        $images = ai_pdf_to_images($pdf_path);

        if (!$images['success']) {
            return $images;
        }

        $extracted_texts = [];
        $prompt = $options['prompt'] ?? 'Extract all text from this image. Maintain the structure and formatting as much as possible. Return the text in markdown format.';

        foreach ($images['data'] as $index => $image_data) {
            switch ($provider) {
                case 'openai':
                    $result = ai_openai_vision($image_data, $prompt, $options);
                    break;
                case 'gemini':
                    $result = ai_gemini_vision($image_data, $prompt, $options);
                    break;
                case 'claude':
                    // Claude vision implementation would go here
                    $result = ['success' => false, 'error' => 'Claude vision not implemented yet'];
                    break;
                default:
                    return ['success' => false, 'error' => 'Unsupported AI provider'];
            }

            if ($result['success']) {
                $extracted_texts[] = [
                    'page' => $index + 1,
                    'text' => ai_extract_content_from_response($result['data'], $provider)
                ];
            } else {
                $extracted_texts[] = [
                    'page' => $index + 1,
                    'text' => '',
                    'error' => $result['error']
                ];
            }
        }

        return [
            'success' => true,
            'data' => $extracted_texts,
            'total_pages' => count($images['data'])
        ];
    }
}

if (!function_exists('ai_pdf_to_images')) {
    /**
     * Convert PDF to images for AI processing
     *
     * @param string $pdf_path Path to PDF file
     * @return array Array of base64 encoded images
     */
    function ai_pdf_to_images($pdf_path) {
        // This is a placeholder - you'll need to implement PDF to image conversion
        // You can use libraries like Imagick or external tools like pdf2pic

        return [
            'success' => false,
            'error' => 'PDF to image conversion not implemented. Please implement using Imagick or similar library.'
        ];
    }
}

if (!function_exists('ai_extract_content_from_response')) {
    /**
     * Extract content from AI response based on provider
     *
     * @param array $response_data Raw response from AI
     * @param string $provider AI provider name
     * @return string Extracted content
     */
    function ai_extract_content_from_response($response_data, $provider) {
        switch ($provider) {
            case 'openai':
                return $response_data['choices'][0]['message']['content'] ?? '';

            case 'gemini':
                return $response_data['candidates'][0]['content']['parts'][0]['text'] ?? '';

            case 'claude':
                return $response_data['content'][0]['text'] ?? '';

            default:
                return '';
        }
    }
}

if (!function_exists('ai_analyze_job_description')) {
    /**
     * Analyze job description using AI
     *
     * @param string $job_description The job description text
     * @param string $provider AI provider to use
     * @param array $options Additional options
     * @return array Analysis results
     */
    function ai_analyze_job_description($job_description, $provider = 'gemini', $options = []) {
        $prompt = $options['prompt'] ?? 'Analyze this job description and extract the following information in JSON format:
        {
            "position_title": "extracted title",
            "department": "department name",
            "location": "work location",
            "employment_type": "full-time/part-time/contract",
            "salary_range": "salary information if available",
            "key_responsibilities": ["responsibility 1", "responsibility 2"],
            "required_qualifications": ["qualification 1", "qualification 2"],
            "preferred_qualifications": ["preferred 1", "preferred 2"],
            "skills_required": ["skill 1", "skill 2"],
            "experience_required": "years of experience",
            "education_required": "education level",
            "application_deadline": "deadline if mentioned",
            "summary": "brief summary of the position"
        }

        Job Description:
        ' . $job_description;

        switch ($provider) {
            case 'openai':
                $messages = [['role' => 'user', 'content' => $prompt]];
                return ai_openai_chat($messages, $options);

            case 'gemini':
                return ai_gemini_chat($prompt, $options);

            case 'claude':
                $messages = [['role' => 'user', 'content' => $prompt]];
                return ai_claude_chat($messages, $options);

            default:
                return ['success' => false, 'error' => 'Unsupported AI provider'];
        }
    }
}

if (!function_exists('ai_match_candidate_to_job')) {
    /**
     * Match candidate profile to job requirements using AI
     *
     * @param string $candidate_profile Candidate's profile/resume text
     * @param string $job_requirements Job requirements text
     * @param string $provider AI provider to use
     * @param array $options Additional options
     * @return array Matching analysis
     */
    function ai_match_candidate_to_job($candidate_profile, $job_requirements, $provider = 'gemini', $options = []) {
        $prompt = $options['prompt'] ?? 'Analyze how well this candidate matches the job requirements. Provide a detailed analysis in JSON format:
        {
            "overall_match_score": 85,
            "strengths": ["strength 1", "strength 2"],
            "gaps": ["gap 1", "gap 2"],
            "recommendations": ["recommendation 1", "recommendation 2"],
            "qualification_match": {
                "education": {"required": "Bachelor", "candidate": "Master", "match": true},
                "experience": {"required": "5 years", "candidate": "7 years", "match": true}
            },
            "skills_analysis": {
                "matched_skills": ["skill 1", "skill 2"],
                "missing_skills": ["skill 3", "skill 4"]
            },
            "summary": "Overall assessment summary"
        }

        Job Requirements:
        ' . $job_requirements . '

        Candidate Profile:
        ' . $candidate_profile;

        switch ($provider) {
            case 'openai':
                $messages = [['role' => 'user', 'content' => $prompt]];
                return ai_openai_chat($messages, $options);

            case 'gemini':
                return ai_gemini_chat($prompt, $options);

            case 'claude':
                $messages = [['role' => 'user', 'content' => $prompt]];
                return ai_claude_chat($messages, $options);

            default:
                return ['success' => false, 'error' => 'Unsupported AI provider'];
        }
    }
}

if (!function_exists('ai_validate_response')) {
    /**
     * Validate AI response and check for errors
     *
     * @param array $response AI response array
     * @param string $provider AI provider name
     * @return array Validation result
     */
    function ai_validate_response($response, $provider) {
        if (!$response['success']) {
            return [
                'valid' => false,
                'error' => $response['error'],
                'content' => null
            ];
        }

        $content = ai_extract_content_from_response($response['data'], $provider);

        if (empty($content)) {
            return [
                'valid' => false,
                'error' => 'Empty response from AI',
                'content' => null
            ];
        }

        return [
            'valid' => true,
            'error' => null,
            'content' => $content
        ];
    }
}

if (!function_exists('ai_format_for_database')) {
    /**
     * Format AI response for database storage
     *
     * @param string $content AI response content
     * @param string $format Output format ('json', 'text', 'markdown')
     * @return string Formatted content
     */
    function ai_format_for_database($content, $format = 'text') {
        switch ($format) {
            case 'json':
                // Try to parse and re-encode JSON to ensure validity
                $decoded = json_decode($content, true);
                return $decoded ? json_encode($decoded, JSON_PRETTY_PRINT) : $content;

            case 'markdown':
                // Clean up markdown formatting
                return trim($content);

            case 'text':
            default:
                // Remove extra whitespace and normalize line endings
                return trim(preg_replace('/\s+/', ' ', $content));
        }
    }
}

if (!function_exists('ai_log_request')) {
    /**
     * Log AI request for debugging and monitoring
     *
     * @param string $provider AI provider
     * @param string $function Function called
     * @param array $params Request parameters
     * @param array $response Response data
     * @param float $execution_time Execution time in seconds
     */
    function ai_log_request($provider, $function, $params, $response, $execution_time) {
        $log_data = [
            'timestamp' => date('Y-m-d H:i:s'),
            'provider' => $provider,
            'function' => $function,
            'success' => $response['success'],
            'execution_time' => round($execution_time, 3),
            'http_code' => $response['http_code'] ?? null,
            'error' => $response['error'] ?? null,
            'token_usage' => ai_extract_token_usage($response['data'] ?? [], $provider)
        ];

        // Log to CodeIgniter log
        log_message('info', 'AI Request: ' . json_encode($log_data));

        // Optionally save to database for analytics
        // You can implement database logging here if needed
    }
}

if (!function_exists('ai_extract_token_usage')) {
    /**
     * Extract token usage information from AI response
     *
     * @param array $response_data Raw response data
     * @param string $provider AI provider
     * @return array Token usage information
     */
    function ai_extract_token_usage($response_data, $provider) {
        switch ($provider) {
            case 'openai':
                return $response_data['usage'] ?? [];

            case 'gemini':
                return $response_data['usageMetadata'] ?? [];

            case 'claude':
                return $response_data['usage'] ?? [];

            default:
                return [];
        }
    }
}

if (!function_exists('ai_estimate_tokens')) {
    /**
     * Estimate token count for text (rough approximation)
     *
     * @param string $text Input text
     * @return int Estimated token count
     */
    function ai_estimate_tokens($text) {
        // Rough estimation: 1 token ≈ 4 characters for English text
        return ceil(strlen($text) / 4);
    }
}

if (!function_exists('ai_chunk_text')) {
    /**
     * Split large text into chunks for AI processing
     *
     * @param string $text Input text
     * @param int $max_tokens Maximum tokens per chunk
     * @param int $overlap Overlap between chunks in tokens
     * @return array Array of text chunks
     */
    function ai_chunk_text($text, $max_tokens = 3000, $overlap = 200) {
        $max_chars = $max_tokens * 4; // Rough conversion
        $overlap_chars = $overlap * 4;

        if (strlen($text) <= $max_chars) {
            return [$text];
        }

        $chunks = [];
        $start = 0;

        while ($start < strlen($text)) {
            $end = $start + $max_chars;

            if ($end >= strlen($text)) {
                $chunks[] = substr($text, $start);
                break;
            }

            // Try to break at sentence boundary
            $break_pos = strrpos(substr($text, $start, $max_chars), '. ');
            if ($break_pos !== false) {
                $end = $start + $break_pos + 1;
            }

            $chunks[] = substr($text, $start, $end - $start);
            $start = $end - $overlap_chars;
        }

        return $chunks;
    }
}
